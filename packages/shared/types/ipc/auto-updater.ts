/**
 * 更新检查结果
 */
export interface UpdateCheckResult {
  /**
   * 是否有可用更新
   */
  hasUpdate: boolean
  /**
   * 新版本号（仅在有更新时返回）
   */
  version?: string
}

/**
 * 更新启动结果
 */
export interface UpdateStartResult {
  /**
   * 是否成功启动更新
   */
  started: boolean
}

/**
 * 自动更新器IPC客户端接口
 */
export interface AutoUpdaterIPCClient {
  /**
   * 检查更新
   * @returns 更新检查结果
   */
  checkForUpdates(): Promise<UpdateCheckResult>

  /**
   * 开始更新
   * @returns 更新启动结果
   */
  startUpdate(): Promise<UpdateStartResult>
}
