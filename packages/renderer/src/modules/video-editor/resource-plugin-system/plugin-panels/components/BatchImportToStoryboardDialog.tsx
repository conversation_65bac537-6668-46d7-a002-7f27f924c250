import React, { useC<PERSON>back, useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { MaterialResource } from '@/types/resources'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { TrackType } from '@/modules/video-editor/types'
import { Overlay, OverlayType } from '@clipnest/remotion-shared/types'
import { DEFAULT_OVERLAY, FPS } from '@/modules/video-editor/constants'
import { generateNewOverlayId } from '@/modules/video-editor/utils/track-helper'
import { ResourceModule } from '@/libs/request/api/resource'
import { toast } from 'react-toastify'
import { cloneDeep } from 'lodash'

interface BatchImportToStoryboardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  folderData: MaterialResource.Media | null
  projectId: string
}

export const BatchImportToStoryboardDialog: React.FC<BatchImportToStoryboardDialogProps> = ({
  open,
  onOpenChange,
  folderData,
  projectId
}) => {
  const { tracks, updateTracks, getAspectRatioDimensions } = useEditorContext()
  const [selectedStoryboard, setSelectedStoryboard] = useState<number | null>(null)

  // 获取所有分镜
  const storyboards = tracks
    .find(track => track.type === TrackType.STORYBOARD)
    ?.overlays
    .filter(overlay => overlay.type === OverlayType.STORYBOARD)
    .map((overlay, index) => ({
      id: overlay.id,
      index,
      content: overlay.title || `分镜 ${index + 1}`,
      from: overlay.from,
      durationInFrames: overlay.durationInFrames
    })) || []

  // 计算视频在播放器中的适配尺寸
  const calculateVideoSize = useCallback(
    (mediaWidth?: number, mediaHeight?: number) => {
      const playerDimensions = getAspectRatioDimensions()
      const playerWidth = playerDimensions.width
      const playerHeight = playerDimensions.height

      const mediaAspectRatio = (mediaWidth ?? playerWidth) / (mediaHeight ?? playerHeight)
      const playerAspectRatio = playerWidth / playerHeight

      let width: number
      let height: number

      if (mediaAspectRatio > playerAspectRatio) {
      // 视频更宽，以播放器宽度为准
        width = playerWidth
        height = playerWidth / mediaAspectRatio
      } else {
      // 视频更高，以播放器高度为准
        height = playerHeight
        width = playerHeight * mediaAspectRatio
      }

      return { width: Math.round(width), height: Math.round(height) }
    },
    [getAspectRatioDimensions]
  )

  // 找到指定分镜索引下的空视频轨道
  const findEmptyVideoTracksForStoryboard = useCallback((storyboardIndex: number) => {
    return tracks
      .map((track, trackIndex) => ({ track, trackIndex }))
      .filter(({ track }) => track.type === TrackType.VIDEO && !track.isGlobalTrack)
      .filter(({ track }) => {
        // 检查轨道中是否已存在该分镜的 overlay
        return !track.overlays.some(overlay => overlay.storyboardIndex === storyboardIndex)
      })
  }, [tracks])

  // 确认导入到选中的分镜
  const handleConfirmImport = useCallback(async () => {
    if (!folderData || selectedStoryboard === null) return

    try {
      const res = await ResourceModule.media.list({
        pageSize: 100,
        pageNo: 1,
        projectId: Number(projectId),
        folderUuid: folderData.fileId,
        sortField: MaterialResource.SortField.UPLOAD_TIME,
        sortOrder: MaterialResource.SortOrder.ASC,
      })

      const folderResources = res?.list || []
      if (folderResources.length === 0) {
        toast.warning('该文件夹下没有媒体文件资源')
        return
      }

      // 过滤出视频文件
      const videoFiles = folderResources.filter(file =>
        file.resType === MaterialResource.MediaType.VIDEO
      )

      if (videoFiles.length === 0) {
        toast.warning('该文件夹下没有视频文件')
        return
      }

      // 找到目标分镜
      const targetStoryboard = storyboards[selectedStoryboard]
      if (!targetStoryboard) {
        toast.error('选中的分镜不存在')
        return
      }

      // 找到该分镜下的空视频轨道
      const emptyVideoTracks = findEmptyVideoTracksForStoryboard(selectedStoryboard)

      // 计算需要的轨道数量
      const requiredTracks = videoFiles.length
      const availableTracks = emptyVideoTracks.length
      const tracksToCreate = Math.max(0, requiredTracks - availableTracks)

      // 构造所有新的 overlays
      const newOverlays: Overlay[] = videoFiles
        .map((mediaFile, index) => {
          const { width, height } = calculateVideoSize(mediaFile.width, mediaFile.height)
          return {
            ...DEFAULT_OVERLAY,
            id: generateNewOverlayId(tracks) + index,
            type: OverlayType.VIDEO,
            content: mediaFile.fileName,
            src: mediaFile.url,
            originalDurationInFrames: (mediaFile.duration ?? 0) / 1000 * FPS,
            from: targetStoryboard.from,
            durationInFrames: FPS * 3, // 默认3秒
            storyboardIndex: selectedStoryboard, // 确保所有 overlay 具有相同的 storyboardIndex
            width,
            height,
            left: 0,
            top: 0,
            styles: {
              opacity: 1,
              zIndex: 100,
              transform: 'none',
              objectFit: 'cover' as const,
              volume: 1
            }
          }
        })

      // 一次性更新所有轨道
      updateTracks(prevTracks => {
        const updatedTracks = cloneDeep(prevTracks)

        // 创建需要的新轨道
        for (let i = 0; i < tracksToCreate; i++) {
          updatedTracks.push({
            type: TrackType.VIDEO,
            overlays: [],
            isGlobalTrack: false
          })
        }

        // 获取所有可用的轨道（包括新创建的）
        const allAvailableTracks = [
          ...emptyVideoTracks,
          // 添加新创建的轨道信息
          ...Array.from({ length: tracksToCreate }, (_, i) => ({
            trackIndex: updatedTracks.length - tracksToCreate + i,
            track: updatedTracks[updatedTracks.length - tracksToCreate + i]
          }))
        ]

        // 为每个新 overlay 分配到对应的轨道
        newOverlays.forEach((overlay, index) => {
          const targetTrackIndex = allAvailableTracks[index].trackIndex
          updatedTracks[targetTrackIndex].overlays.push(overlay)
        })

        return updatedTracks
      })

      const createdMessage = tracksToCreate > 0
        ? `，并创建了 ${tracksToCreate} 个新轨道`
        : ''

      toast.success(`成功导入 ${newOverlays.length} 个视频到分镜 ${selectedStoryboard + 1}${createdMessage}`)

      // 关闭对话框并重置状态
      onOpenChange(false)
      setSelectedStoryboard(null)
    } catch (error) {
      console.error('导入到分镜失败:', error)
      toast.error('导入到分镜失败')
    }
  }, [folderData, selectedStoryboard, storyboards, tracks, findEmptyVideoTracksForStoryboard, updateTracks, projectId, onOpenChange, calculateVideoSize])

  const handleClose = useCallback(() => {
    onOpenChange(false)
    setSelectedStoryboard(null)
  }, [onOpenChange])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>选择目标分镜</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          {storyboards.length === 0 ? (
            <p className="text-gray-500 text-center">当前项目中没有分镜</p>
          ) : (
            <div className="space-y-3">
              <p className="text-sm text-gray-600 mb-4">
                请选择要导入素材的分镜：
              </p>
              {storyboards.map((storyboard, index) => {
                const emptyTracks = findEmptyVideoTracksForStoryboard(index)
                return (
                  <label
                    key={storyboard.id}
                    className={`flex items-center p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedStoryboard === index
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 hover:border-gray-300 dark:border-gray-700'
                    }`}
                  >
                    <input
                      type="radio"
                      name="storyboard"
                      value={index}
                      checked={selectedStoryboard === index}
                      onChange={() => setSelectedStoryboard(index)}
                      className="mr-3"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-sm">
                        {storyboard.content}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        时长: {(storyboard.durationInFrames / FPS).toFixed(1)}秒 |
                        可用轨道: {emptyTracks.length}
                        {emptyTracks.length === 0 && (
                          <span className="text-blue-600 ml-1">(将自动创建新轨道)</span>
                        )}
                      </div>
                    </div>
                  </label>
                )
              })}
            </div>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            取消
          </Button>
          <Button
            onClick={handleConfirmImport}
            disabled={selectedStoryboard === null}
          >
            确认导入
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
