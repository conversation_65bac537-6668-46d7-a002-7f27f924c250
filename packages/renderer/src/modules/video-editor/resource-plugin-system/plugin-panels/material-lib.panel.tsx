import React, { memo, useCallback, useState } from 'react'
import { MaterialResource, ResourceSource } from '@/types/resources'
import { RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { SortMenu } from '@/pages/Projects/material/components/MaterialFilterBar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { SearchInput } from '@/components/ui/search-input'
import MediaTypeSelector from '@/pages/Projects/material/components/MediaTypeSelector'
import UploadMaterial from '@/pages/Projects/material/components/UploadMaterial'
import MediaItem, { FolderAction, MediaAction } from '@/pages/Projects/material/components/MediaItem'
import MoveDialog from '@/pages/Projects/material/components/MoveDialog'
import { InfiniteResourceList } from '@/components/InfiniteResourceList'
import Breadcrumbs from '@/components/Breadcrumbs'
import { useItemActions } from '@/hooks/useItemActions'
import { useFolderActions } from '@/hooks/useFolderActions'
import { useMediaActions } from '@/hooks/useMediaActions'
import { ResourceModule } from '@/libs/request/api/resource'
import { useEditorContext } from '@/modules/video-editor/contexts'
import {
  EditorDraggableTypes,
  EditorDroppableTypes,
  useTypedDraggable,
  useTypedDroppable,
} from '../../components/editor-dnd-wrapper'
import { useMaterialManager } from '@/hooks/useMaterialManager'
import BatchActionButtons from '@/pages/Projects/material/components/BatchActionButtons'

type EditorMediaItemProps = {
  item: MaterialResource.Media
  mediaActions: MediaAction[]
  selectedMediaItems: Set<string>
  toggleSelect: (fileId: string, isFolder: boolean) => void
}

const EditorMediaItem: React.FC<EditorMediaItemProps> = ({ item, mediaActions, selectedMediaItems, toggleSelect }) => {
  const { setNodeRef, listeners, attributes } = useTypedDraggable(EditorDraggableTypes.Resource, item.fileId, {
    resource: {
      ...item,
      resourceType: ResourceSource.MEDIA,
    },
  })

  return (
    <div ref={setNodeRef} {...listeners} {...attributes}>
      <MediaItem
        isEditItem={true}
        orientation="horizontal"
        media={item}
        isSelected={selectedMediaItems.has(item.fileId)}
        isFolder={false}
        actions={mediaActions}
        onToggleSelect={fileId => toggleSelect(fileId, false)}
      />
    </div>
  )
}

const EditorFolderItem = ({
  folder,
  folderActions,
  selectedFolderItems,
  toggleSelect,
  handleFolderClick,
  onbatchImportStoryboards, // 批量导入到分镜
}: {
  folder: MaterialResource.Media
  folderActions: FolderAction[]
  selectedFolderItems: Set<string>
  toggleSelect: (fileId: string, isFolder: boolean) => void
  handleFolderClick: (folderId: string) => void
  onRefresh: () => Promise<void>
  onbatchImportStoryboards: () => void
}) => {
  const { setNodeRef, listeners, attributes } = useTypedDraggable(EditorDraggableTypes.Folder, `floder-Draggable-${folder.fileId}`, {
    resource: {
      ...folder,
      resourceType: ResourceSource.FOLDER
    }
  })
  const { setNodeRef: setDroppableRef } = useTypedDroppable(EditorDroppableTypes.Folder, `floder-Droppable-${folder.fileId}`, {
    resource: {
      ...folder,
      resourceType: ResourceSource.FOLDER
    }
  })

  return (
    <div
      ref={node => {
        setNodeRef(node) // Draggable
        setDroppableRef(node) // Droppable
      }}
      {...listeners}
      {...attributes}
    >
      <MediaItem
        key={folder.fileId}
        data-type="folder"
        isEditItem={true}
        orientation="horizontal"
        media={folder}
        isSelected={selectedFolderItems.has(folder.fileId)}
        isFolder={true}
        actions={folderActions}
        onToggleSelect={fileId => toggleSelect(fileId, true)}
        onFolderClick={() => handleFolderClick(folder.fileId)}
        onbatchImportStoryboards={onbatchImportStoryboards}
      />
    </div>
  )
}

/**
 * 素材库组件
 */
export function MaterialLibPanel() {
  const { projectId } = useEditorContext()

  const [activeTab, setActiveTab] = useState(0)
  const [moveDialogOpen, setMoveDialogOpen] = useState(false)
  const [moveId, setMoveId] = useState('')
  const [moveType, setMoveType] = useState(ResourceSource.FOLDER)

  const { createItem, renameItem, deleteItem } = useItemActions()
  const folderActions = useFolderActions(createItem, renameItem, deleteItem, setMoveType, setMoveId, setMoveDialogOpen)
  const mediaActions = useMediaActions(renameItem, deleteItem, setMoveType, setMoveId, setMoveDialogOpen)

  const materialManager = useMaterialManager({
    projectId: Number(projectId),
  })

  const {
    filters,
    setFilters,
    treeData,
    currentFolderId,
    folderPath,
    filteredFolders,
    mediaQueryResult,
    selection,
    onRefresh,
    handleBatchDelete,
    handleFolderClick,
    handleMoveConfirm,
  } = materialManager

  const inputs = [
    { placeholder: '搜索关键词', field: 'keyword' },
    { placeholder: '合成次数', field: 'useCountRange', isNumber: true },
    { placeholder: '引用次数', field: 'quoteCountRange', isNumber: true },
  ]
  const handleBatchImportStoryboards = useCallback(async (data: MaterialResource.Media) => {
    try {
      console.log('批量导入到分镜')
      console.log('点击的文件夹数据', data)
      const res = await ResourceModule.media.list({
        pageSize: 100, // 取足够多的文件
        pageNo: 1,
        projectId: Number(projectId),
        folderUuid: data.fileId,
        sortField: MaterialResource.SortField.UPLOAD_TIME,
        sortOrder: MaterialResource.SortOrder.ASC,
      })

      const folderResources = res?.list || []
      if (folderResources.length > 0) {
        console.log('点击的文件夹下的媒体文件数据', folderResources)
      } else {
        console.log('该文件夹下没有媒体文件资源')
      }
    } catch (error) {
      console.error('获取文件夹媒体文件资源失败:', error)
    }
  }, [])

  // 素材包装组件，处理异步加载状态
  const MediaItemWrapper = useCallback(
    ({ item, index }: { item: MaterialResource.Media; index: number }) => (
      <EditorMediaItem
        key={`media-${item.fileId}-${index}`}
        item={item}
        mediaActions={mediaActions}
        selectedMediaItems={selection.selectedMediaItems}
        toggleSelect={selection.toggleSelect}
      />
    ),
    [mediaActions],
  )

  const renderMediaItem = useCallback(
    (item: MaterialResource.Media, index: number) => {
      return <MediaItemWrapper item={item} index={index} />
    },
    [MediaItemWrapper],
  )

  const renderMediaContent = useCallback(() => {
    const folderItems = filteredFolders.map(folder => (
      <EditorFolderItem
        key={`folder-${folder.fileId}`}
        folder={folder}
        folderActions={folderActions}
        selectedFolderItems={selection.selectedFolderItems}
        toggleSelect={selection.toggleSelect}
        handleFolderClick={handleFolderClick}
        onRefresh={onRefresh}
        onbatchImportStoryboards={() => handleBatchImportStoryboards(folder)}
      />
    ))
    return (
      <div>
        <InfiniteResourceList
          queryResult={mediaQueryResult}
          renderItem={renderMediaItem}
          emptyText="暂无素材"
          loadingText="加载素材中..."
          itemsContainerClassName="flex flex-wrap gap-3 pt-3 pb-3"
          headerContent={folderItems}
        />
      </div>
    )
  }, [mediaQueryResult, renderMediaItem])

  return (
    <div className="flex flex-col h-full w-full overflow-hidden px-2 py-1">
      <div className="flex justify-between gap-1 py-1">
        {/* 上传 */}
        <UploadMaterial
          folderUuid={filters.folderUuid}
          onUpload={async () => {
            onRefresh()
          }}
        />

        {/* 新建文件夹 */}
        <Button
          variant="ghost"
          size="sm"
          className="px-4 h-7 bg-primary/10 shadow-md"
          onClick={() =>
            createItem(ResourceSource.FOLDER, filters.folderUuid, {
              label: '文件夹名称',
              headerTitle: '文件夹',
            })}
        >
          新建文件夹
        </Button>
      </div>
      <div className="flex justify-between mt-2">
        {/* 下拉列表：树列表 */}
        <Select
          value={folderPath?.[0]?.id?.toString() ?? undefined}
          onValueChange={value => setFilters(prev => ({ ...prev, folderUuid: value }))}
        >
          <SelectTrigger
            className="w-32 mb-2 text-sm border-0 bg-primary/10 h-7 w-full mr-2"
            style={{ fontSize: '12px' }}
          >
            <SelectValue placeholder="项目" />
          </SelectTrigger>
          <SelectContent>
            {treeData?.map(opt => (
              <SelectItem key={opt.id} value={opt.id}>
                {opt.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {/* 排序 */}
        <SortMenu
          sortField={filters.sortField}
          sortOrder={filters.sortOrder}
          size="sm"
          onChange={(field, order) =>
            setFilters(prev => ({
              ...prev,
              sortField: field,
              sortOrder: order,
            }))}
        />
      </div>
      {/* 输入筛选 */}
      <div className="flex gap-1">
        {inputs.map(({ placeholder, field, isNumber }) => (
          <SearchInput
            key={field}
            placeholder={placeholder}
            value={filters[field] ?? ''}
            onChange={e =>
              setFilters(prev => ({
                ...prev,
                [field]: isNumber ? Number(e.target.value) : e.target.value,
              }))}
            containerClassName="w-30%"
            size="xs"
          />
        ))}
      </div>
      {/* 分类筛选 */}
      <div>
        <MediaTypeSelector activeTab={activeTab} isEdit={true} setActiveTab={setActiveTab} setFilters={setFilters} />
      </div>

      {selection.selectedCount ? (
        <div className="flex items-center justify-between text-sm ">
          <div className="flex justify-end items-center space-x-4">
            <span>总数：{selection.mediaCount}</span>
            <label className="flex items-center cursor-pointer">
              <input
                type="radio"
                checked={selection.allSelected}
                onClick={selection.toggleSelectAll}
                readOnly
                className="accent-primary-highlight1"
              />
            </label>
            <span>已选 {selection.selectedCount}</span>
          </div>
          {/*  多选-移动到/删除 */}
          <BatchActionButtons
            variant="link"
            highlightClass="bg-primary-highlight1"
            deleteName={`${selection.selectedCount}个素材`}
            onMove={() => {
              setMoveType(ResourceSource.MULTI_SELECT)
              setMoveDialogOpen(true)
            }}
            onDelete={handleBatchDelete}
          />
        </div>
      ) : (
        <div className="flex items-center justify-between text-sm text-gray-600">
          {/* 面包屑导航 */}
          <Breadcrumbs folderPath={folderPath} currentFolderId={currentFolderId} onFolderClick={handleFolderClick} />
          <RefreshCw className="inline-block w-4 h-4 mr-1 text-primary-highlight1" onClick={onRefresh} />
        </div>
      )}

      <div className="mt-2 overflow-y-auto">{renderMediaContent()}</div>
      <MoveDialog
        open={moveDialogOpen}
        moveId={moveId}
        moveType={moveType}
        onOpenChange={setMoveDialogOpen}
        onConfirm={selectedNode => handleMoveConfirm(selectedNode, moveType)}
      />
    </div>
  )
}

export default memo(MaterialLibPanel)
