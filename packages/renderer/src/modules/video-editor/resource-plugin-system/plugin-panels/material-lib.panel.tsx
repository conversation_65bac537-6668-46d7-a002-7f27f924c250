import React, { memo, useCallback, useState } from 'react'
import { MaterialResource, ResourceSource } from '@/types/resources'
import { RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { SortMenu } from '@/pages/Projects/material/components/MaterialFilterBar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { SearchInput } from '@/components/ui/search-input'
import MediaTypeSelector from '@/pages/Projects/material/components/MediaTypeSelector'
import UploadMaterial from '@/pages/Projects/material/components/UploadMaterial'
import MediaItem, { FolderAction, MediaAction } from '@/pages/Projects/material/components/MediaItem'
import MoveDialog from '@/pages/Projects/material/components/MoveDialog'
import { InfiniteResourceList } from '@/components/InfiniteResourceList'
import Breadcrumbs from '@/components/Breadcrumbs'
import { useItemActions } from '@/hooks/useItemActions'
import { useFolderActions } from '@/hooks/useFolderActions'
import { useMediaActions } from '@/hooks/useMediaActions'
import { ResourceModule } from '@/libs/request/api/resource'
import { useEditorContext } from '@/modules/video-editor/contexts'
import {
  EditorDraggableTypes,
  EditorDroppableTypes,
  useTypedDraggable,
  useTypedDroppable,
} from '../../components/editor-dnd-wrapper'
import { useMaterialManager } from '@/hooks/useMaterialManager'
import BatchActionButtons from '@/pages/Projects/material/components/BatchActionButtons'
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { OverlayType, StoryboardOverlay, VideoOverlay } from '@clipnest/remotion-shared/types'
import { TrackType } from '@/modules/video-editor/types'
import { DEFAULT_OVERLAY, FPS } from '@/modules/video-editor/constants'
import { generateNewOverlayId } from '@/modules/video-editor/utils/track-helper'
import { useToast } from '@/hooks/useToast'

type EditorMediaItemProps = {
  item: MaterialResource.Media
  mediaActions: MediaAction[]
  selectedMediaItems: Set<string>
  toggleSelect: (fileId: string, isFolder: boolean) => void
}

const EditorMediaItem: React.FC<EditorMediaItemProps> = ({ item, mediaActions, selectedMediaItems, toggleSelect }) => {
  const { setNodeRef, listeners, attributes } = useTypedDraggable(EditorDraggableTypes.Resource, item.fileId, {
    resource: {
      ...item,
      resourceType: ResourceSource.MEDIA,
    },
  })

  return (
    <div ref={setNodeRef} {...listeners} {...attributes}>
      <MediaItem
        isEditItem={true}
        orientation="horizontal"
        media={item}
        isSelected={selectedMediaItems.has(item.fileId)}
        isFolder={false}
        actions={mediaActions}
        onToggleSelect={fileId => toggleSelect(fileId, false)}
      />
    </div>
  )
}

const EditorFolderItem = ({
  folder,
  folderActions,
  selectedFolderItems,
  toggleSelect,
  handleFolderClick,
  onbatchImportStoryboards, // 批量导入到分镜
}: {
  folder: MaterialResource.Media
  folderActions: FolderAction[]
  selectedFolderItems: Set<string>
  toggleSelect: (fileId: string, isFolder: boolean) => void
  handleFolderClick: (folderId: string) => void
  onRefresh: () => Promise<void>
  onbatchImportStoryboards: () => void
}) => {
  const { setNodeRef, listeners, attributes } = useTypedDraggable(EditorDraggableTypes.Folder, `floder-Draggable-${folder.fileId}`, {
    resource: {
      ...folder,
      resourceType: ResourceSource.FOLDER
    }
  })
  const { setNodeRef: setDroppableRef } = useTypedDroppable(EditorDroppableTypes.Folder, `floder-Droppable-${folder.fileId}`, {
    resource: {
      ...folder,
      resourceType: ResourceSource.FOLDER
    }
  })

  return (
    <div
      ref={node => {
        setNodeRef(node) // Draggable
        setDroppableRef(node) // Droppable
      }}
      {...listeners}
      {...attributes}
    >
      <MediaItem
        key={folder.fileId}
        data-type="folder"
        isEditItem={true}
        orientation="horizontal"
        media={folder}
        isSelected={selectedFolderItems.has(folder.fileId)}
        isFolder={true}
        actions={folderActions}
        onToggleSelect={fileId => toggleSelect(fileId, true)}
        onFolderClick={() => handleFolderClick(folder.fileId)}
        onbatchImportStoryboards={onbatchImportStoryboards}
      />
    </div>
  )
}

/**
 * 素材库组件
 */
export function MaterialLibPanel() {
  const { projectId } = useEditorContext()

  const [activeTab, setActiveTab] = useState(0)
  const [moveDialogOpen, setMoveDialogOpen] = useState(false)
  const [moveId, setMoveId] = useState('')
  const [moveType, setMoveType] = useState(ResourceSource.FOLDER)

  const { createItem, renameItem, deleteItem } = useItemActions()
  const folderActions = useFolderActions(createItem, renameItem, deleteItem, setMoveType, setMoveId, setMoveDialogOpen)
  const mediaActions = useMediaActions(renameItem, deleteItem, setMoveType, setMoveId, setMoveDialogOpen)

  const materialManager = useMaterialManager({
    projectId: Number(projectId),
  })

  const {
    filters,
    setFilters,
    treeData,
    currentFolderId,
    folderPath,
    filteredFolders,
    mediaQueryResult,
    selection,
    onRefresh,
    handleBatchDelete,
    handleFolderClick,
    handleMoveConfirm,
  } = materialManager

  const inputs = [
    { placeholder: '搜索关键词', field: 'keyword' },
    { placeholder: '合成次数', field: 'useCountRange', isNumber: true },
    { placeholder: '引用次数', field: 'quoteCountRange', isNumber: true },
  ]
  // 批量导入到分镜的状态管理
  const [importDialogOpen, setImportDialogOpen] = useState(false)
  const [selectedStoryboardIndex, setSelectedStoryboardIndex] = useState<number | null>(null)
  const [isImporting, setIsImporting] = useState(false)
  const [currentFolderData, setCurrentFolderData] = useState<MaterialResource.Media | null>(null)

  const { tracks, updateTracks, getAspectRatioDimensions } = useEditorContext()
  const { toast } = useToast()

  // 获取所有分镜信息
  const getStoryboards = useCallback(() => {
    const storyboardTrack = tracks.find(track => track.type === TrackType.STORYBOARD)
    if (!storyboardTrack) return []

    return storyboardTrack.overlays
      .filter(overlay => overlay.type === OverlayType.STORYBOARD)
      .sort((a, b) => a.from - b.from)
      .map((overlay, index) => ({
        ...overlay as StoryboardOverlay,
        index,
        durationInSeconds: Math.round((overlay.durationInFrames / FPS) * 10) / 10
      }))
  }, [tracks])

  const handleBatchImportStoryboards = useCallback(async (data: MaterialResource.Media) => {
    setCurrentFolderData(data)
    setImportDialogOpen(true)
    setSelectedStoryboardIndex(null)
  }, [])

  // 处理导入确认
  const handleImportConfirm = useCallback(async () => {
    if (!currentFolderData || selectedStoryboardIndex === null) {
      toast({
        title: '请选择目标分镜',
        variant: 'destructive'
      })
      return
    }

    setIsImporting(true)

    try {
      // 获取文件夹中的媒体文件
      const res = await ResourceModule.media.list({
        pageSize: 100,
        pageNo: 1,
        projectId: Number(projectId),
        folderUuid: currentFolderData.fileId,
        sortField: MaterialResource.SortField.UPLOAD_TIME,
        sortOrder: MaterialResource.SortOrder.ASC,
      })

      const folderResources = res?.list || []

      // 过滤出视频文件
      const videoFiles = folderResources.filter(
        file => file.resType === MaterialResource.MediaType.VIDEO
      )

      if (videoFiles.length === 0) {
        toast({
          title: '该文件夹中没有视频文件',
          variant: 'destructive'
        })
        return
      }

      // 获取选中的分镜信息
      const storyboards = getStoryboards()
      const targetStoryboard = storyboards[selectedStoryboardIndex]

      if (!targetStoryboard) {
        toast({
          title: '选中的分镜不存在',
          variant: 'destructive'
        })
        return
      }

      // 获取播放器尺寸
      const { width: playerWidth, height: playerHeight } = getAspectRatioDimensions()

      // 创建 VideoOverlay 数组
      const newVideoOverlays: VideoOverlay[] = videoFiles.map((file, index) => {
        // 计算视频在分镜中的起始位置
        const startFrame = targetStoryboard.from + (index * DEFAULT_OVERLAY.durationInFrames)

        // 确保不超出分镜范围
        const maxEndFrame = targetStoryboard.from + targetStoryboard.durationInFrames
        const actualDuration = Math.min(
          DEFAULT_OVERLAY.durationInFrames,
          maxEndFrame - startFrame
        )

        if (actualDuration <= 0) {
          return null // 如果没有空间了就跳过
        }

        return {
          ...DEFAULT_OVERLAY,
          id: generateNewOverlayId(tracks) + index,
          type: OverlayType.VIDEO,
          content: file.fileName || '',
          src: file.url || '',
          from: startFrame,
          durationInFrames: actualDuration,
          originalDurationInFrames: file.duration ? Math.round(file.duration / 1000 * FPS) : DEFAULT_OVERLAY.durationInFrames,
          storyboardIndex: selectedStoryboardIndex,
          width: playerWidth,
          height: playerHeight,
          left: 0,
          top: 0,
          videoStartTime: 0,
          styles: {
            opacity: 1,
            zIndex: 100,
            transform: 'none',
            objectFit: 'cover' as const,
            volume: 1
          }
        } as VideoOverlay
      }).filter(Boolean) as VideoOverlay[]

      if (newVideoOverlays.length === 0) {
        toast({
          title: '分镜空间不足，无法添加视频',
          variant: 'destructive'
        })
        return
      }

      // 更新轨道，将视频添加到视频轨道中
      updateTracks(prevTracks => {
        const newTracks = [...prevTracks]

        // 找到一个空的视频轨道或创建新的视频轨道
        let targetTrackIndex = newTracks.findIndex(
          track => track.type === TrackType.VIDEO && !track.isGlobalTrack && track.overlays.length === 0
        )

        if (targetTrackIndex === -1) {
          // 创建新的视频轨道
          newTracks.push({
            type: TrackType.VIDEO,
            isGlobalTrack: false,
            overlays: []
          })
          targetTrackIndex = newTracks.length - 1
        }

        // 将新的视频 overlays 添加到轨道中
        newTracks[targetTrackIndex] = {
          ...newTracks[targetTrackIndex],
          overlays: [...newTracks[targetTrackIndex].overlays, ...newVideoOverlays]
        }

        return newTracks
      })

      toast({
        title: `成功导入 ${newVideoOverlays.length} 个视频到分镜 "${targetStoryboard.title || `第${selectedStoryboardIndex + 1}个分镜`}"`
      })

      setImportDialogOpen(false)
      setSelectedStoryboardIndex(null)
      setCurrentFolderData(null)
    } catch (error) {
      console.error('批量导入到分镜失败:', error)
      toast({
        title: '导入失败',
        description: '请检查网络连接或稍后重试',
        variant: 'destructive'
      })
    } finally {
      setIsImporting(false)
    }
  }, [currentFolderData, selectedStoryboardIndex, tracks, updateTracks, getAspectRatioDimensions, getStoryboards, projectId, toast])

  // 素材包装组件，处理异步加载状态
  const MediaItemWrapper = useCallback(
    ({ item, index }: { item: MaterialResource.Media; index: number }) => (
      <EditorMediaItem
        key={`media-${item.fileId}-${index}`}
        item={item}
        mediaActions={mediaActions}
        selectedMediaItems={selection.selectedMediaItems}
        toggleSelect={selection.toggleSelect}
      />
    ),
    [mediaActions],
  )

  const renderMediaItem = useCallback(
    (item: MaterialResource.Media, index: number) => {
      return <MediaItemWrapper item={item} index={index} />
    },
    [MediaItemWrapper],
  )

  const renderMediaContent = useCallback(() => {
    const folderItems = filteredFolders.map(folder => (
      <EditorFolderItem
        key={`folder-${folder.fileId}`}
        folder={folder}
        folderActions={folderActions}
        selectedFolderItems={selection.selectedFolderItems}
        toggleSelect={selection.toggleSelect}
        handleFolderClick={handleFolderClick}
        onRefresh={onRefresh}
        onbatchImportStoryboards={() => handleBatchImportStoryboards(folder)}
      />
    ))
    return (
      <div>
        <InfiniteResourceList
          queryResult={mediaQueryResult}
          renderItem={renderMediaItem}
          emptyText="暂无素材"
          loadingText="加载素材中..."
          itemsContainerClassName="flex flex-wrap gap-3 pt-3 pb-3"
          headerContent={folderItems}
        />
      </div>
    )
  }, [mediaQueryResult, renderMediaItem])

  return (
    <div className="flex flex-col h-full w-full overflow-hidden px-2 py-1">
      <div className="flex justify-between gap-1 py-1">
        {/* 上传 */}
        <UploadMaterial
          folderUuid={filters.folderUuid}
          onUpload={async () => {
            onRefresh()
          }}
        />

        {/* 新建文件夹 */}
        <Button
          variant="ghost"
          size="sm"
          className="px-4 h-7 bg-primary/10 shadow-md"
          onClick={() =>
            createItem(ResourceSource.FOLDER, filters.folderUuid, {
              label: '文件夹名称',
              headerTitle: '文件夹',
            })}
        >
          新建文件夹
        </Button>
      </div>
      <div className="flex justify-between mt-2">
        {/* 下拉列表：树列表 */}
        <Select
          value={folderPath?.[0]?.id?.toString() ?? undefined}
          onValueChange={value => setFilters(prev => ({ ...prev, folderUuid: value }))}
        >
          <SelectTrigger
            className="w-32 mb-2 text-sm border-0 bg-primary/10 h-7 w-full mr-2"
            style={{ fontSize: '12px' }}
          >
            <SelectValue placeholder="项目" />
          </SelectTrigger>
          <SelectContent>
            {treeData?.map(opt => (
              <SelectItem key={opt.id} value={opt.id}>
                {opt.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {/* 排序 */}
        <SortMenu
          sortField={filters.sortField}
          sortOrder={filters.sortOrder}
          size="sm"
          onChange={(field, order) =>
            setFilters(prev => ({
              ...prev,
              sortField: field,
              sortOrder: order,
            }))}
        />
      </div>
      {/* 输入筛选 */}
      <div className="flex gap-1">
        {inputs.map(({ placeholder, field, isNumber }) => (
          <SearchInput
            key={field}
            placeholder={placeholder}
            value={filters[field] ?? ''}
            onChange={e =>
              setFilters(prev => ({
                ...prev,
                [field]: isNumber ? Number(e.target.value) : e.target.value,
              }))}
            containerClassName="w-30%"
            size="xs"
          />
        ))}
      </div>
      {/* 分类筛选 */}
      <div>
        <MediaTypeSelector activeTab={activeTab} isEdit={true} setActiveTab={setActiveTab} setFilters={setFilters} />
      </div>

      {selection.selectedCount ? (
        <div className="flex items-center justify-between text-sm ">
          <div className="flex justify-end items-center space-x-4">
            <span>总数：{selection.mediaCount}</span>
            <label className="flex items-center cursor-pointer">
              <input
                type="radio"
                checked={selection.allSelected}
                onClick={selection.toggleSelectAll}
                readOnly
                className="accent-primary-highlight1"
              />
            </label>
            <span>已选 {selection.selectedCount}</span>
          </div>
          {/*  多选-移动到/删除 */}
          <BatchActionButtons
            variant="link"
            highlightClass="bg-primary-highlight1"
            deleteName={`${selection.selectedCount}个素材`}
            onMove={() => {
              setMoveType(ResourceSource.MULTI_SELECT)
              setMoveDialogOpen(true)
            }}
            onDelete={handleBatchDelete}
          />
        </div>
      ) : (
        <div className="flex items-center justify-between text-sm text-gray-600">
          {/* 面包屑导航 */}
          <Breadcrumbs folderPath={folderPath} currentFolderId={currentFolderId} onFolderClick={handleFolderClick} />
          <RefreshCw className="inline-block w-4 h-4 mr-1 text-primary-highlight1" onClick={onRefresh} />
        </div>
      )}

      <div className="mt-2 overflow-y-auto">{renderMediaContent()}</div>
      <MoveDialog
        open={moveDialogOpen}
        moveId={moveId}
        moveType={moveType}
        onOpenChange={setMoveDialogOpen}
        onConfirm={selectedNode => handleMoveConfirm(selectedNode, moveType)}
      />

      {/* 批量导入到分镜对话框 */}
      <Dialog open={importDialogOpen} onOpenChange={setImportDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>选择目标分镜</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              将文件夹 "{currentFolderData?.fileName}" 中的视频导入到以下分镜：
            </p>

            <div className="space-y-2 max-h-60 overflow-y-auto">
              {getStoryboards().map((storyboard, index) => (
                <label
                  key={storyboard.id}
                  className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                >
                  <input
                    type="radio"
                    name="storyboard"
                    value={index}
                    checked={selectedStoryboardIndex === index}
                    onChange={() => setSelectedStoryboardIndex(index)}
                    className="accent-primary"
                  />
                  <div className="flex-1">
                    <div className="font-medium">
                      {storyboard.title || `第${index + 1}个分镜`}
                    </div>
                    <div className="text-sm text-gray-500">
                      时长: {storyboard.durationInSeconds}秒
                    </div>
                  </div>
                </label>
              ))}
            </div>

            {getStoryboards().length === 0 && (
              <p className="text-sm text-gray-500 text-center py-4">
                当前项目中没有分镜
              </p>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setImportDialogOpen(false)}
              disabled={isImporting}
            >
              取消
            </Button>
            <Button
              onClick={handleImportConfirm}
              disabled={selectedStoryboardIndex === null || isImporting || getStoryboards().length === 0}
            >
              {isImporting ? '导入中...' : '确认导入'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default memo(MaterialLibPanel)
