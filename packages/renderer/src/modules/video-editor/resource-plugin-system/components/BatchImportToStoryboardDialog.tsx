import React, { useState, useC<PERSON>back, useMemo } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { MaterialResource } from '@/types/resources'
import { OverlayType, VideoOverlay, StoryboardOverlay } from '@clipnest/remotion-shared/types'
import { TrackType, Track } from '@/modules/video-editor/types'
import { DEFAULT_OVERLAY, FPS } from '@/modules/video-editor/constants'
import { generateNewOverlayId } from '@/modules/video-editor/utils/track-helper'
import { useToast } from '@/hooks/useToast'
import { ResourceModule } from '@/libs/request/api/resource'

interface BatchImportToStoryboardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  folderData: MaterialResource.Media | null
  tracks: Track[]
  updateTracks: (updater: React.SetStateAction<Track[]>) => void
  getAspectRatioDimensions: () => { width: number; height: number }
  projectId: string
}

interface StoryboardInfo extends StoryboardOverlay {
  index: number
  durationInSeconds: number
}

export const BatchImportToStoryboardDialog: React.FC<BatchImportToStoryboardDialogProps> = ({
  open,
  onOpenChange,
  folderData,
  tracks,
  updateTracks,
  getAspectRatioDimensions,
  projectId
}) => {
  const [selectedStoryboardIndex, setSelectedStoryboardIndex] = useState<number | null>(null)
  const [isImporting, setIsImporting] = useState(false)
  const { toast } = useToast()

  // 获取所有分镜信息
  const storyboards = useMemo((): StoryboardInfo[] => {
    const storyboardTrack = tracks.find(track => track.type === TrackType.STORYBOARD)
    if (!storyboardTrack) return []

    return storyboardTrack.overlays
      .filter(overlay => overlay.type === OverlayType.STORYBOARD)
      .sort((a, b) => a.from - b.from)
      .map((overlay, index) => ({
        ...overlay as StoryboardOverlay,
        index,
        durationInSeconds: Math.round((overlay.durationInFrames / FPS) * 10) / 10
      }))
  }, [tracks])

  // 检查轨道在指定时间范围内是否有冲突
  const hasOverlayConflictInRange = useCallback((track: Track, startFrame: number, endFrame: number): boolean => {
    return track.overlays.some(overlay => {
      const overlayStart = overlay.from
      const overlayEnd = overlay.from + overlay.durationInFrames
      // 检查是否有重叠
      return !(overlayEnd <= startFrame || overlayStart >= endFrame)
    })
  }, [])

  // 找到合适的视频轨道
  const findSuitableVideoTrack = useCallback((targetStoryboard: StoryboardInfo): number => {
    const storyboardStart = targetStoryboard.from
    const storyboardEnd = targetStoryboard.from + targetStoryboard.durationInFrames

    // 查找在目标分镜时间范围内没有 Overlay 的现有视频轨道
    const suitableTrackIndex = tracks.findIndex(track => 
      track.type === TrackType.VIDEO && 
      !track.isGlobalTrack &&
      !hasOverlayConflictInRange(track, storyboardStart, storyboardEnd)
    )

    return suitableTrackIndex
  }, [tracks, hasOverlayConflictInRange])

  // 创建 VideoOverlay 数组
  const createVideoOverlays = useCallback((
    videoFiles: MaterialResource.Media[], 
    targetStoryboard: StoryboardInfo,
    playerDimensions: { width: number; height: number }
  ): VideoOverlay[] => {
    const baseId = generateNewOverlayId(tracks)
    
    return videoFiles.map((file, index) => {
      // 计算视频在分镜中的起始位置
      const startFrame = targetStoryboard.from + (index * DEFAULT_OVERLAY.durationInFrames)
      
      // 确保不超出分镜范围
      const maxEndFrame = targetStoryboard.from + targetStoryboard.durationInFrames
      const actualDuration = Math.min(
        DEFAULT_OVERLAY.durationInFrames,
        maxEndFrame - startFrame
      )

      if (actualDuration <= 0) {
        return null // 如果没有空间了就跳过
      }

      return {
        ...DEFAULT_OVERLAY,
        id: baseId + index,
        type: OverlayType.VIDEO,
        content: file.fileName || '',
        src: file.url || '',
        from: startFrame,
        durationInFrames: actualDuration,
        originalDurationInFrames: file.duration ? Math.round(file.duration * FPS) : DEFAULT_OVERLAY.durationInFrames,
        storyboardIndex: targetStoryboard.index, // 修复：使用一致的 storyboardIndex
        width: playerDimensions.width,
        height: playerDimensions.height,
        left: 0,
        top: 0,
        videoStartTime: 0,
        styles: {
          opacity: 1,
          zIndex: 100,
          transform: 'none',
          objectFit: 'cover' as const,
          volume: 1
        }
      } as VideoOverlay
    }).filter(Boolean) as VideoOverlay[]
  }, [tracks])

  // 处理导入确认
  const handleImportConfirm = useCallback(async () => {
    if (!folderData || selectedStoryboardIndex === null) {
      toast({
        title: '请选择目标分镜',
        variant: 'destructive'
      })
      return
    }

    const targetStoryboard = storyboards[selectedStoryboardIndex]
    if (!targetStoryboard) {
      toast({
        title: '选中的分镜不存在',
        variant: 'destructive'
      })
      return
    }

    setIsImporting(true)

    try {
      // 获取文件夹中的媒体文件
      const res = await ResourceModule.media.list({
        pageSize: 100,
        pageNo: 1,
        projectId: Number(projectId),
        folderUuid: folderData.fileId,
        sortField: MaterialResource.SortField.UPLOAD_TIME,
        sortOrder: MaterialResource.SortOrder.ASC,
      })

      const folderResources = res?.list || []
      
      // 过滤出视频文件
      const videoFiles = folderResources.filter(
        file => file.resType === MaterialResource.MediaType.VIDEO
      )

      if (videoFiles.length === 0) {
        toast({
          title: '该文件夹中没有视频文件',
          variant: 'destructive'
        })
        return
      }

      // 获取播放器尺寸
      const playerDimensions = getAspectRatioDimensions()

      // 创建 VideoOverlay 数组
      const newVideoOverlays = createVideoOverlays(videoFiles, targetStoryboard, playerDimensions)

      if (newVideoOverlays.length === 0) {
        toast({
          title: '分镜空间不足，无法添加视频',
          variant: 'destructive'
        })
        return
      }

      // 更新轨道，将视频添加到合适的视频轨道中
      updateTracks(prevTracks => {
        const newTracks = [...prevTracks]
        
        // 优化轨道选择策略：找到在目标分镜时间范围内没有冲突的轨道
        let targetTrackIndex = findSuitableVideoTrack(targetStoryboard)

        if (targetTrackIndex === -1) {
          // 如果没有合适的轨道，创建新的视频轨道
          newTracks.push({
            type: TrackType.VIDEO,
            isGlobalTrack: false,
            overlays: []
          })
          targetTrackIndex = newTracks.length - 1
        }

        // 将新的视频 overlays 添加到轨道中
        newTracks[targetTrackIndex] = {
          ...newTracks[targetTrackIndex],
          overlays: [...newTracks[targetTrackIndex].overlays, ...newVideoOverlays]
        }

        return newTracks
      })

      toast({
        title: `成功导入 ${newVideoOverlays.length} 个视频到分镜 "${targetStoryboard.title || `第${selectedStoryboardIndex + 1}个分镜`}"`
      })

      // 重置状态并关闭对话框
      setSelectedStoryboardIndex(null)
      onOpenChange(false)

    } catch (error) {
      console.error('批量导入到分镜失败:', error)
      toast({
        title: '导入失败',
        description: '请检查网络连接或稍后重试',
        variant: 'destructive'
      })
    } finally {
      setIsImporting(false)
    }
  }, [
    folderData, 
    selectedStoryboardIndex, 
    storyboards, 
    projectId, 
    getAspectRatioDimensions, 
    createVideoOverlays, 
    updateTracks, 
    findSuitableVideoTrack, 
    toast, 
    onOpenChange
  ])

  // 重置状态当对话框关闭时
  const handleOpenChange = useCallback((open: boolean) => {
    if (!open) {
      setSelectedStoryboardIndex(null)
      setIsImporting(false)
    }
    onOpenChange(open)
  }, [onOpenChange])

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>选择目标分镜</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            将文件夹 "{folderData?.fileName}" 中的视频导入到以下分镜：
          </p>
          
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {storyboards.map((storyboard, index) => (
              <label
                key={storyboard.id}
                className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
              >
                <input
                  type="radio"
                  name="storyboard"
                  value={index}
                  checked={selectedStoryboardIndex === index}
                  onChange={() => setSelectedStoryboardIndex(index)}
                  className="accent-primary"
                />
                <div className="flex-1">
                  <div className="font-medium">
                    {storyboard.title || `第${index + 1}个分镜`}
                  </div>
                  <div className="text-sm text-gray-500">
                    时长: {storyboard.durationInSeconds}秒
                  </div>
                </div>
              </label>
            ))}
          </div>

          {storyboards.length === 0 && (
            <p className="text-sm text-gray-500 text-center py-4">
              当前项目中没有分镜
            </p>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => handleOpenChange(false)}
            disabled={isImporting}
          >
            取消
          </Button>
          <Button
            onClick={handleImportConfirm}
            disabled={selectedStoryboardIndex === null || isImporting || storyboards.length === 0}
          >
            {isImporting ? '导入中...' : '确认导入'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
