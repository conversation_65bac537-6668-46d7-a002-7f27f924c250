import React, { FC, useMemo } from 'react'
import clsx from 'clsx'
import { Camera, Grip, Image, Layers, Mic, Music, Type, Video } from 'lucide-react'
import { ENABLE_TRACK_DRAG } from '@/modules/video-editor/constants'
import { WithTooltip } from '@/components/WithTooltip'
import { getTrackTypeLabel } from '@/modules/video-editor/utils/track-helper'
import { Track, TrackType } from '@/modules/video-editor/types'

type TimelineTrackHeaderProps = Pick<Track, 'type' | 'isGlobalTrack'> & {
  trackIndex: number
  typeIndex?: number
}

const IconByTrackType: Partial<Record<TrackType, FC>> = {
  [TrackType.VIDEO]: Video,
  [TrackType.SOUND]: Music,
  [TrackType.TEXT]: Type,
  [TrackType.IMAGE]: Image,
  [TrackType.MIXED]: Layers,
  [TrackType.NARRATION]: Mic,
  [TrackType.STORYBOARD]: Camera,
}

export const TimelineTrackHeader: FC<TimelineTrackHeaderProps> = ({
  type, typeIndex, isGlobalTrack
}) => {
  const trackLabel = useMemo(() => {
    if (type === TrackType.STORYBOARD) return '分镜轨道'

    return `${isGlobalTrack ? '全局' : '分镜'}${getTrackTypeLabel(type)}轨道${typeof typeIndex === 'number' ? `-${typeIndex}` : ''}`
  }, [isGlobalTrack, type, typeIndex])

  return (
    <>
      <div
        className={clsx(
          'flex items-center justify-center transition-all duration-200 border-2',
        )}
      >
        {ENABLE_TRACK_DRAG && (
          <div
            className={clsx(
              `flex items-center justify-center rounded-md
              transition-all duration-150
              active:scale-95
              group`,
            )}
          >
            <Grip
              className={clsx(
                'w-3 h-3 text-gray-400 dark:text-gray-500 transition-colors duration-150',
              )}
            />
          </div>
        )}
      </div>

      {type && IconByTrackType[type] && (
        <WithTooltip content={trackLabel}>
          <div className="size-5 flex justify-center items-center gap-px border rounded-xs">
            {/* @ts-ignore*/}
            {React.createElement(IconByTrackType[type], { className: clsx('text-gray-500', typeof typeIndex === 'number' ? 'size-[10px]' : 'size-3') })}
            {typeof typeIndex === 'number' && (
              <div className="text-[9px] font-thin text-gray-500">{typeIndex}</div>
            )}
          </div>
        </WithTooltip>
      )}
    </>
  )
}
