import React, { useCallback, useEffect, useRef, useState } from 'react'
import { cn } from '@/components/lib/utils'

export interface AudioProgressBarProps {
  /**
   * 当前播放时间（秒）
   */
  currentTime: number
  /**
   * 音频总时长（秒）
   */
  duration: number
  /**
   * 是否正在播放
   */
  isPlaying: boolean
  /**
   * 寻轨回调
   */
  onSeek: (time: number) => void
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 是否显示时间标签
   */
  showTimeLabels?: boolean
  /**
   * 进度条高度
   */
  height?: number
}

/**
 * 可拖动的音频进度条组件
 * 支持点击和拖动定位，提供实时视觉反馈
 */
export function AudioProgressBar({
  currentTime,
  duration,
  isPlaying,
  onSeek,
  className = '',
  showTimeLabels = false,
  height = 4
}: AudioProgressBarProps) {
  const progressBarRef = useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragTime, setDragTime] = useState(0)

  // 计算进度百分比
  const progress = duration > 0 ? (currentTime / duration) * 100 : 0
  const displayProgress = isDragging ? (dragTime / duration) * 100 : progress

  // 格式化时间显示
  const formatTime = useCallback((time: number): string => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }, [])

  // 根据鼠标位置计算时间
  const calculateTimeFromPosition = useCallback((clientX: number): number => {
    if (!progressBarRef.current || duration <= 0) return 0

    const rect = progressBarRef.current.getBoundingClientRect()
    const x = clientX - rect.left
    const percentage = Math.max(0, Math.min(1, x / rect.width))
    return percentage * duration
  }, [duration])

  // 处理点击事件
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    if (duration <= 0) return

    const time = calculateTimeFromPosition(e.clientX)
    onSeek(time)
  }, [duration, calculateTimeFromPosition, onSeek])

  // 处理鼠标按下事件
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()

    if (duration <= 0) return

    setIsDragging(true)
    const time = calculateTimeFromPosition(e.clientX)
    setDragTime(time)

    // 添加全局鼠标事件监听器
    const handleMouseMove = (e: MouseEvent) => {
      const time = calculateTimeFromPosition(e.clientX)
      setDragTime(time)
      // 在拖拽过程中提供实时反馈
      onSeek(time)
    }

    const handleMouseUp = (e: MouseEvent) => {
      const time = calculateTimeFromPosition(e.clientX)
      onSeek(time)
      setIsDragging(false)
      setDragTime(0)

      // 移除事件监听器
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }, [duration, calculateTimeFromPosition, onSeek])

  // 处理触摸事件（移动端支持）
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    e.stopPropagation()
    e.preventDefault()

    if (duration <= 0) return

    setIsDragging(true)
    const touch = e.touches[0]
    const time = calculateTimeFromPosition(touch.clientX)
    setDragTime(time)

    const handleTouchMove = (e: TouchEvent) => {
      const touch = e.touches[0]
      const time = calculateTimeFromPosition(touch.clientX)
      setDragTime(time)
      // 在拖拽过程中提供实时反馈
      onSeek(time)
    }

    const handleTouchEnd = (e: TouchEvent) => {
      const touch = e.changedTouches[0]
      const time = calculateTimeFromPosition(touch.clientX)
      onSeek(time)
      setIsDragging(false)
      setDragTime(0)

      // 移除事件监听器
      document.removeEventListener('touchmove', handleTouchMove)
      document.removeEventListener('touchend', handleTouchEnd)
    }

    document.addEventListener('touchmove', handleTouchMove)
    document.addEventListener('touchend', handleTouchEnd)
  }, [duration, calculateTimeFromPosition, onSeek])

  // 防止拖拽时选中文本
  useEffect(() => {
    if (isDragging) {
      document.body.style.userSelect = 'none'
      return () => {
        document.body.style.userSelect = ''
      }
    }
  }, [isDragging])

  return (
    <div className={cn('w-full', className)}>
      {/* 时间标签 */}
      {showTimeLabels && (
        <div className="flex justify-between text-xs text-muted-foreground mb-1">
          <span>{formatTime(isDragging ? dragTime : currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>
      )}

      {/* 进度条容器 */}
      <div
        ref={progressBarRef}
        className={cn(
          'relative w-full bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer',
          'hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors',
          isDragging && 'bg-gray-300 dark:bg-gray-600'
        )}
        style={{ height: `${height}px` }}
        onClick={handleClick}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
      >
        {/* 已播放进度 */}
        <div
          className={cn(
            'absolute top-0 left-0 h-full rounded-full transition-all',
            isPlaying ? 'bg-blue-500' : 'bg-gray-400',
            isDragging && 'bg-blue-600'
          )}
          style={{
            width: `${Math.max(0, Math.min(100, displayProgress))}%`,
            transition: isDragging ? 'none' : 'width 0.1s ease-out'
          }}
        />

        {/* 拖拽手柄 */}
        {(isDragging || displayProgress > 0) && (
          <div
            className={cn(
              'absolute top-1/2 w-3 h-3 bg-white border-2 rounded-full shadow-sm',
              'transform -translate-y-1/2 -translate-x-1/2 transition-all',
              isPlaying ? 'border-blue-500' : 'border-gray-400',
              isDragging ? 'scale-125 border-blue-600' : 'scale-100',
              'hover:scale-110'
            )}
            style={{
              left: `${Math.max(0, Math.min(100, displayProgress))}%`,
              transition: isDragging ? 'none' : 'all 0.1s ease-out'
            }}
          />
        )}
      </div>

      {/* 缓冲进度（可选，如果需要显示缓冲状态） */}
      {/* 这里可以添加缓冲进度的显示逻辑 */}
    </div>
  )
}
