# ClipNest 自动化部署

## 🚀 快速开始

### 1. 环境配置

确保你的 `.env` 文件包含以下配置：

```env
# 腾讯云 COS 配置
COS_SECRET_ID=your_secret_id_here
COS_SECRET_KEY=your_secret_key_here
COS_REGION=ap-guangzhou
COS_BUCKET=dev-common-1304320301

# 构建配置
VITE_DISTRIBUTION_CHANNEL=latest
AUTO_UPLOAD=true
```

### 2. 可用脚本

| 脚本 | 说明 |
|------|------|
| `npm run test:env` | 测试环境变量配置 |
| `npm run test:update-config` | **新增** 测试自动更新配置一致性 |
| `npm run test:simple-upload` | 测试基础上传功能 |
| `npm run create:small-mock-dist` | 创建小型测试文件 |
| `npm run test:small-upload` | 测试小型文件上传 |
| `npm run upload` | 上传到 .env 中配置的渠道 |
| `npm run upload:latest` | 上传到 latest 渠道 |
| `npm run upload:beta` | 上传到 beta 渠道 |
| `npm run upload:alpha` | 上传到 alpha 渠道 |

### 3. 使用流程

1. **测试配置**：
   ```bash
   npm run test:env
   npm run test:update-config  # 新增：验证自动更新配置
   npm run test:simple-upload
   ```

2. **测试构建产物上传**：
   ```bash
   npm run create:small-mock-dist
   npm run test:small-upload
   ```

3. **实际构建并上传**：
   ```bash
   # 使用 .env 中配置的渠道（推荐）
   npm run package
   npm run upload

   # 或者指定特定渠道
   npm run package
   npm run upload:latest  # 或 upload:beta, upload:alpha
   ```

### 4. 渠道说明

**渠道的作用**：
- 渠道决定了应用从哪个路径检查和下载更新
- 你的 `.env` 文件中的 `VITE_DISTRIBUTION_CHANNEL=dev` 意味着应用会从 `dev` 渠道检查更新
- 上传脚本会自动使用相同的渠道，确保一致性

**渠道类型**：
- `dev` - 开发版本（你当前使用的）
- `latest` - 正式发布版本
- `beta` - 测试版本
- `alpha` - 内测版本

**重要**：确保上传的渠道与应用配置的渠道一致！

### 5. COS 存储结构（优化版）

```
clipnest-update/
├── beta/                     # 测试版渠道（当前使用的）
│   └── latest/
│       ├── beta.yml          # Windows 更新清单
│       ├── beta-mac.yml      # macOS 更新清单
│       ├── beta-linux.yml    # Linux 更新清单
│       ├── clipnest-1.0.0-beta.1-win.exe      # Windows 安装包
│       ├── clipnest-1.0.0-beta.1-win.exe.blockmap
│       ├── clipnest-1.0.0-beta.1-mac.dmg      # macOS 安装包
│       ├── clipnest-1.0.0-beta.1-mac.dmg.blockmap
│       ├── clipnest-1.0.0-beta.1-linux.deb    # Linux 安装包
│       └── clipnest-1.0.0-beta.1-linux.deb.blockmap
├── latest/                   # 正式版渠道
│   └── latest/
│       ├── latest.yml        # Windows 更新清单
│       ├── latest-mac.yml    # macOS 更新清单
│       ├── latest-linux.yml  # Linux 更新清单
│       └── ...               # 对应的安装包文件
├── dev/                      # 开发版渠道
└── alpha/                    # 内测版渠道
```

**重要变更**：
- 所有文件现在都放在 `{channel}/latest/` 根目录下
- yml 文件按平台命名：`{channel}.yml`, `{channel}-mac.yml`, `{channel}-linux.yml`
- 这种结构与 electron-builder 的期望完全匹配
- 移除了复杂的平台子目录结构，简化了路径管理
- 只上传自动更新必需的文件：`.exe`、`.blockmap`、`.yml`

### 5. 故障排除

- 如果上传失败，检查 COS 配置是否正确
- 确保网络连接正常
- 验证存储桶权限设置

## ✅ 已验证功能

- ✅ 环境变量配置测试
- ✅ 基础文件上传功能
- ✅ 多平台构建产物上传
- ✅ 版本化存储
- ✅ 多渠道支持
- ✅ 公共访问验证
